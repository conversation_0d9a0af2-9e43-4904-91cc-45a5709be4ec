package tndung.vnfb.smm.rest;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import tndung.vnfb.smm.constant.SMMKeyParam;
import tndung.vnfb.smm.dto.CommentOrderSMM;
import tndung.vnfb.smm.dto.OrderSMM;
import tndung.vnfb.smm.dto.response.smm.*;
import tndung.vnfb.smm.entity.ApiProvider;

import java.util.List;

@Component
@RequiredArgsConstructor
@Slf4j
public class SMMConsumerImpl implements SMMConsumer {
    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;

    /**
     * Helper method to parse JSON from response body regardless of content type
     */
    private String extractJsonFromResponse(ResponseEntity<String> response) {
        String responseBody = response.getBody();
        if (responseBody == null) {
            return "{}";
        }

        MediaType contentType = response.getHeaders().getContentType();
        log.debug("Response content type: {}", contentType);
        log.debug("Response body: {}", responseBody);

        // If content type is HTML, try to extract JSON from the response
        if (contentType != null && contentType.includes(MediaType.TEXT_HTML)) {
            log.info("Received HTML response, attempting to parse as JSON");
            // Some APIs return HTML content type but JSON body
            // Try to parse the body directly as JSON
            try {
                new JSONObject(responseBody); // Test if it's valid JSON
                return responseBody;
            } catch (Exception e) {
                log.warn("HTML response body is not valid JSON: {}", responseBody);
                throw new RuntimeException("Invalid JSON response received with HTML content type");
            }
        }

        return responseBody;
    }

    private ResponseEntity<String> postMultipart(String url,
                                                 MultiValueMap<String, Object> map) {

        final HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        // Accept both JSON and HTML responses
        headers.setAccept(List.of(MediaType.APPLICATION_JSON, MediaType.TEXT_HTML, MediaType.ALL));

        final HttpEntity<MultiValueMap<String, Object>> request = new HttpEntity<>(map, headers);
        return restTemplate
                .exchange(url,
                        HttpMethod.POST,
                        request, String.class);
    }

    @Override
    public JSONObject call(ApiProvider apiProvider, MultiValueMap<String, Object> param) {
        param.add(SMMKeyParam.KEY, apiProvider.getSecretKey());

        ResponseEntity<String> response = postMultipart(apiProvider.getUrl(), param);
        String jsonBody = extractJsonFromResponse(response);
        return new JSONObject(jsonBody);
    }


    /**
     * Helper method to parse object from JSON response regardless of content type
     */
    private <T> T parseObjectFromResponse(ResponseEntity<String> response, Class<T> type) {
        try {
            String jsonBody = extractJsonFromResponse(response);
            return objectMapper.readValue(jsonBody, type);
        } catch (Exception e) {
            log.error("Failed to parse response to {}: {}", type.getSimpleName(), e.getMessage());
            throw new RuntimeException("Failed to parse API response", e);
        }
    }

    public <T> T callObject(ApiProvider apiProvider, MultiValueMap<String, Object> param, Class<T> type) {
        param.add(SMMKeyParam.KEY, apiProvider.getSecretKey());

        // Use String response first, then parse manually to handle content type issues
        ResponseEntity<String> response = postMultipart(apiProvider.getUrl(), param);
        return parseObjectFromResponse(response, type);
    }

    public SMMBalanceRes getBalance(ApiProvider apiProvider) {
        MultiValueMap<String, Object> param = new LinkedMultiValueMap<>();
        param.add(SMMKeyParam.ACTION, SMMKeyParam.ActionName.BALANCE);

        return callObject(apiProvider, param, SMMBalanceRes.class);
    }

    public JSONObject getOrderStatus(ApiProvider apiProvider, String orderId) {
        MultiValueMap<String, Object> param = new LinkedMultiValueMap<>();
        param.add(SMMKeyParam.ACTION, SMMKeyParam.ActionName.STATUS);
        param.add(SMMKeyParam.ORDER, orderId);


        return call(apiProvider, param);
    }

    public SMMOrderRes addOrderDefault(ApiProvider apiProvider, OrderSMM req) {
        MultiValueMap<String, Object> param = new LinkedMultiValueMap<>();
        param.add(SMMKeyParam.ACTION, SMMKeyParam.ActionName.ADD);
        param.add(SMMKeyParam.SERVICE, req.getApiServiceId());
        param.add(SMMKeyParam.LINK, req.getLink());
        param.add(SMMKeyParam.QUANTITY, req.getQuantity());

        return callObject(apiProvider, param, SMMOrderRes.class);
    }

    @Override
    public SMMOrderRes addOrderComments(ApiProvider apiProvider, CommentOrderSMM req) {
        MultiValueMap<String, Object> param = new LinkedMultiValueMap<>();
        param.add(SMMKeyParam.ACTION, SMMKeyParam.ActionName.ADD);
        param.add(SMMKeyParam.SERVICE, req.getApiServiceId());
        param.add(SMMKeyParam.LINK, req.getLink());
        param.add(SMMKeyParam.COMMENTS, req.getComments());

        return callObject(apiProvider, param, SMMOrderRes.class);
    }

    @Override
    public SMMRefillRes refill(ApiProvider apiProvider, String orderId) {
        MultiValueMap<String, Object> param = new LinkedMultiValueMap<>();
        param.add(SMMKeyParam.ACTION, SMMKeyParam.ActionName.REFILL);
        param.add(SMMKeyParam.ORDER, orderId);
        return callObject(apiProvider, param, SMMRefillRes.class);
    }


    @Override
    public Object cancel(ApiProvider apiProvider, String orderId) {
        MultiValueMap<String, Object> param = new LinkedMultiValueMap<>();
        param.add(SMMKeyParam.ACTION, SMMKeyParam.ActionName.CANCEL);
        param.add(SMMKeyParam.ORDER, orderId);
        param.add(SMMKeyParam.KEY, apiProvider.getSecretKey());

        try {
            // Get response as String first to handle content type issues
            ResponseEntity<String> response = postMultipart(apiProvider.getUrl(), param);
            String jsonBody = extractJsonFromResponse(response);

            if (jsonBody == null || jsonBody.trim().isEmpty() || jsonBody.equals("{}")) {
                throw new RuntimeException("Empty response body");
            }

            try {
                // Try to parse as SMMOrderCancellation2 first
                return objectMapper.readValue(jsonBody, SMMOrderCancellation2.class);
            } catch (Exception e) {
                log.debug("Failed to parse response as SMMOrderCancellation2: {}", e.getMessage());

                try {
                    // Try to parse as List<SMMOrderCancellation>
                    return objectMapper.readValue(jsonBody,
                        objectMapper.getTypeFactory().constructCollectionType(List.class, SMMOrderCancellation.class));
                } catch (Exception ex) {
                    log.error("Failed to parse cancel response as any known type. Response: {}", jsonBody);
                    throw new RuntimeException("API call failed: " + ex.getMessage(), ex);
                }
            }
        } catch (Exception e) {
            log.error("Cancel API call failed for order {}: {}", orderId, e.getMessage());
            throw new RuntimeException("API call failed: " + e.getMessage(), e);
        }
    }

    @Override
    public List<SMMServiceRes> getServices(ApiProvider apiProvider) {
        MultiValueMap<String, Object> param = new LinkedMultiValueMap<>();
        param.add(SMMKeyParam.ACTION, SMMKeyParam.ActionName.SERVICES);
        param.add(SMMKeyParam.KEY, apiProvider.getSecretKey());

        try {
            // Get response as String first to handle content type issues
            ResponseEntity<String> response = postMultipart(apiProvider.getUrl(), param);
            String jsonBody = extractJsonFromResponse(response);

            // Parse as List<SMMServiceRes>
            return objectMapper.readValue(jsonBody,
                objectMapper.getTypeFactory().constructCollectionType(List.class, SMMServiceRes.class));
        } catch (Exception e) {
            log.error("Failed to get services from provider {}: {}", apiProvider.getName(), e.getMessage());
            throw new RuntimeException("Failed to get services: " + e.getMessage(), e);
        }
    }
}
