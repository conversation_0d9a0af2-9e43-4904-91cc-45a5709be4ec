import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, HostListener } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { IconsModule } from '../../../icons/icons.module';
import { TranslateModule } from '@ngx-translate/core';
import { EditLinkComponent } from '../../../components/popup/edit-link/edit-link.component';
import { SetCountComponent } from '../../../components/popup/set-count/set-count.component';
import { SetPartialComponent } from '../../../components/popup/set-partial/set-partial.component';

import { IconDropdownComponent } from "../../../components/common/icon-dropdown/icon-dropdown.component";
import { Clipboard } from '@angular/cdk/clipboard';
import { DateRangePickerComponent } from '../../../components/common/date-range-picker/date-range-picker.component';
import { SearchBoxComponent } from '../../../components/common/search-box/search-box.component';
import { ServiceDropdownComponent } from "../../../components/common/service-dropdown/service-dropdown.component";
import { CategoriesService } from '../../../core/services/categories.service';
import { SuperPlatformRes } from '../../../model/response/super-platform.model';
import { IconBaseModel } from '../../../model/base-model';
import { ToastService } from '../../../core/services/toast.service';

import { AdminOrderService, OrderSearchReq } from '../../../core/services/admin-order.service';
import { OrderRes } from '../../../model/response/order-res.model';
import { Subscription } from 'rxjs';
import { SuperGeneralSvRes } from '../../../model/response/super-general-sv.model';
import { STATUS_FILTERS, StatusFilter } from '../../../shared/constants/status-filters';
import { AdminMenuComponent, AdminMenuItem } from '../../../components/common/admin-menu/admin-menu.component';
import { GlobalLoadingComponent } from "../../common/global-loading/global-loading.component";
import { LoadingService } from '../../../core/services/loading.service';
import { CurrencyService } from '../../../core/services/currency.service';


@Component({
  selector: 'app-admin-orders',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    RouterModule,
    IconsModule,
    TranslateModule,
    EditLinkComponent,
    SetCountComponent,
    SetPartialComponent,

    IconDropdownComponent,
    DateRangePickerComponent,
    SearchBoxComponent,
    ServiceDropdownComponent,
    AdminMenuComponent,
    GlobalLoadingComponent
],
  templateUrl: './admin-orders.component.html',
  styleUrls: ['./admin-orders.component.scss']
})
export class AdminOrdersComponent implements OnInit, OnDestroy {
  orders: OrderRes[] = [];
  services: SuperGeneralSvRes[] = [];
  categories: IconBaseModel[] = [];
  allServices: SuperGeneralSvRes[] = [];
  platforms: SuperPlatformRes[] = [];
  private subscriptions: Subscription[] = [];
  viewMode: 'table' | 'card' = 'table'; // Default to table view, toggle to card view for mobile

  statusFilters: StatusFilter[] = [...STATUS_FILTERS];

  searchTerm: string = '';
  selectedStatus: string = 'all';
  selectedService: string = 'all';
  selectedCategoryId: number | null = null;
  selectedServiceId: number | null = null;
  selectedUserId: number | null = null;
  userIdInput: string = '';
  dateRange: { startDate: Date | null, endDate: Date | null } = {
    startDate: null,
    endDate: null
  };
  showFilters: boolean = false;
  selectAll: boolean = false;
  selectedOrders: number[] = [];
  activeActionMenu: number | null = null;
  menuPosition = { top: 0, left: 0, right: 0 };

  // Pagination
  pagination = {
    pageNumber: 0,
    pageSize: 10,
    totalElements: 0,
    totalPages: 0
  };
  isLoading: boolean = false;

  // Edit link popup
  showEditLinkModal: boolean = false;
  selectedOrderId: number | null = null;
  selectedOrderLink: string = '';

  // Set count popup
  showSetCountModal: boolean = false;
  selectedOrderStartCount: number = 0;

  // Set partial popup
  showSetPartialModal: boolean = false;
  selectedOrderQuantity: number = 0;



  // Bulk action menu
  showBulkActionMenu: boolean = false;
  bulkMenuPosition = { top: 0, left: 0 };

  // Provider details toggle for mobile view
  showProviderDetails: { [key: number]: boolean } = {};

  // Tooltip visibility control
  activeTooltip: number | null = null;
  tooltipTimeout: any = null;
  tooltipPosition = { x: 0, y: 0 };
  tooltipTriggerElement: HTMLElement | null = null;

  // Note tooltip visibility control
  activeNoteTooltip: number | null = null;
  noteTooltipTimeout: any = null;
  noteTooltipPosition = { x: 0, y: 0 };
  noteTooltipTriggerElement: HTMLElement | null = null;

  // Handle window resize events
  @HostListener('window:resize')
  onResize() {
    if (this.activeTooltip !== null || this.activeNoteTooltip !== null) {
      this.updateTooltipPositions();
    }
  }

  // Handle window scroll events
  @HostListener('window:scroll', ['$event'])
  onScroll() {
    if (this.activeTooltip !== null || this.activeNoteTooltip !== null) {
      this.updateTooltipPositions();
    }
  }

  constructor(
    private categoriesService: CategoriesService,
    private clipboard: Clipboard,
    private orderService: AdminOrderService,
    private toastService: ToastService,
    private loadingService: LoadingService,
    private currencyService: CurrencyService
  ) {}

  ngOnInit() {
    this.loadPlatforms();
    this.setupSubscriptions();

    // Check for URL parameters
    const queryParams = new URLSearchParams(window.location.search);
    if (queryParams.has('userId')) {
      this.selectedUserId = Number(queryParams.get('userId'));
      this.userIdInput = queryParams.get('userId') || ''; // Also set the input field
      this.showFilters = true;
    }

    this.loadOrders();
    this.detectMobileDevice();

    // Listen for window resize events to update view mode
    window.addEventListener('resize', () => {
      this.detectMobileDevice();
    });

    // Listen for scroll events to hide tooltips on mobile
    window.addEventListener('scroll', this.handleScroll.bind(this), { passive: true });
    document.addEventListener('touchstart', this.handleTouchStart.bind(this), { passive: true });
  }

  /**
   * Detect if the device is mobile and set the view mode accordingly
   */
  detectMobileDevice(): void {
    // Check if screen width is less than 768px (typical mobile breakpoint)
    if (window.innerWidth < 768) {
      this.viewMode = 'card';
    } else {
      this.viewMode = 'table';
    }
  }

  /**
   * Handle scroll events - hide tooltips on mobile when scrolling
   */
  handleScroll(): void {
    // Hide tooltips when scrolling on mobile to prevent dragging effect
    if (window.innerWidth < 768) {
      this.hideTooltip();
      this.hideNoteTooltip();
    }
  }

  /**
   * Handle touch start events - hide tooltips on touch devices
   */
  handleTouchStart(): void {
    // Hide tooltips when touch starts to prevent issues on mobile
    if (window.innerWidth < 768) {
      this.hideTooltip();
      this.hideNoteTooltip();
    }
  }

  /**
   * Update tooltip positions when scrolling or resizing
   */
  updateTooltipPositions(): void {
    // Update main tooltip position if active
    if (this.activeTooltip !== null && this.tooltipTriggerElement) {
      this.calculateTooltipPosition(this.tooltipTriggerElement, 'main');
    }

    // Update note tooltip position if active
    if (this.activeNoteTooltip !== null && this.noteTooltipTriggerElement) {
      this.calculateTooltipPosition(this.noteTooltipTriggerElement, 'note');
    }
  }

  /**
   * Calculate tooltip position based on trigger element
   */
  private calculateTooltipPosition(triggerElement: HTMLElement, tooltipType: 'main' | 'note'): void {
    const rect = triggerElement.getBoundingClientRect();
    const tooltipWidth = 256; // w-64 = 16rem = 256px
    const tooltipHeight = 120; // Approximate height

    // Calculate position using fixed positioning (relative to viewport)
    let x = rect.left;
    let y = rect.bottom + 5; // 5px gap below the element

    // Adjust if tooltip would go off-screen horizontally
    if (x + tooltipWidth > window.innerWidth) {
      x = window.innerWidth - tooltipWidth - 10; // 10px margin from edge
    }
    if (x < 10) {
      x = 10; // 10px margin from left edge
    }

    // Adjust if tooltip would go off-screen vertically
    if (y + tooltipHeight > window.innerHeight) {
      y = rect.top - tooltipHeight - 5; // Show above instead
    }

    // Update the appropriate tooltip position
    if (tooltipType === 'main') {
      this.tooltipPosition = { x, y };
    } else {
      this.noteTooltipPosition = { x, y };
    }
  }

  setupSubscriptions() {
    // Subscribe to orders data
    this.subscriptions.push(
      this.orderService.orders$.subscribe(orders => {
        this.orders = orders;
        // Reset selection state when orders change
        this.selectedOrders = [];
        this.selectAll = false;
      })
    );

    // Subscribe to pagination data
    this.subscriptions.push(
      this.orderService.pagination$.subscribe(pagination => {
        this.pagination = pagination;
      })
    );

    // Subscribe to loading state
    this.subscriptions.push(
      this.orderService.loading$.subscribe(loading => {
        this.isLoading = loading;
        if (loading) {
          this.loadingService.show('Loading orders...');
        } else {
          this.loadingService.hide();
        }
      })
    );

    // Add global click event listener to close dropdown when clicking outside
    document.addEventListener('click', this.handleDocumentClick.bind(this));
  }

  /**
   * Load orders with current filters
   * @param page The page number (0-based)
   */
  loadOrders(page: number = 0): void {
    const filter: OrderSearchReq = {
      page: page,
      size: this.pagination.pageSize
    };

    if (this.searchTerm) {
      filter.keyword = this.searchTerm;
    }

    if (this.selectedStatus !== 'all') {
      filter.status = this.selectedStatus;
    }

    if (this.selectedServiceId) {
      filter.serviceId = this.selectedServiceId;
    }

    if (this.selectedCategoryId) {
      filter.categoryId = this.selectedCategoryId;
    }

    if (this.selectedUserId) {
      filter.userId = this.selectedUserId;
    }

    // Parse date range if set
    if (this.dateRange) {
      if (this.dateRange.startDate) {
        filter.from = this.formatDate(this.dateRange.startDate);
      }
      if (this.dateRange.endDate) {
        filter.to = this.formatDate(this.dateRange.endDate);
      }
    }

    this.orderService.search(filter);
  }

  ngOnDestroy() {
    // Unsubscribe from all subscriptions
    this.subscriptions.forEach(sub => sub.unsubscribe());

    // Remove global click event listener
    document.removeEventListener('click', this.handleDocumentClick.bind(this));

    // Remove resize event listener
    window.removeEventListener('resize', () => {
      this.detectMobileDevice();
    });

    // Remove scroll and touch event listeners
    window.removeEventListener('scroll', this.handleScroll.bind(this));
    document.removeEventListener('touchstart', this.handleTouchStart.bind(this));

    // Clear any tooltip timeout
    if (this.tooltipTimeout) {
      clearTimeout(this.tooltipTimeout);
      this.tooltipTimeout = null;
    }

    if (this.noteTooltipTimeout) {
      clearTimeout(this.noteTooltipTimeout);
      this.noteTooltipTimeout = null;
    }

    // Make sure to hide the loading indicator when component is destroyed
    this.loadingService.hide();
  }

  /**
   * Load platforms and categories from the API
   */
  loadPlatforms(): void {
    this.categoriesService.getPlatforms().subscribe({
      next: (platforms) => {
        this.platforms = platforms
          .filter(platform => !platform.hide)
          .sort((a, b) => a.sort - b.sort);

        // Create categories array from platforms data
        this.categories = [
          // Add an "All Categories" option at the top without an icon
          {
            id: 'all',
            label: 'filter.all_categories',
            sort: 0,
            icon: '' // No icon
          }
        ];
        this.allServices = [];

        platforms.forEach(platform => {
          // Sort categories by sort field before processing them
          const sortedCategories = [...platform.categories]
            .filter(category => !category.hide)
            .sort((a, b) => a.sort - b.sort);

          sortedCategories.forEach(category => {
            this.categories.push({
              id: category.id.toString(),
              label: category.name,
              icon: platform.icon,
              sort: category.sort
            });

            // Add all services to allServices array

              this.allServices.push(...category.services);

          });
        });

        // Select first category by default if available
        if (this.categories.length > 0) {
          this.onCategorySelected(this.categories[0]);
        }
      },
      error: (error) => {
        console.error('Error loading platforms:', error);
        this.toastService.showError(error?.message || 'Failed to load platforms and categories');
      }
    });
  }

  onSearch(term: string) {
    this.searchTerm = term;
    this.loadOrders(); // Keep immediate filtering for search
  }

  onStatusFilterClick(status: any) {
    this.statusFilters.forEach(filter => filter.active = false);
    status.active = true;
    this.selectedStatus = status.value;
    this.loadOrders(); // Keep immediate filtering for status changes
  }

  onDateRangeChange(range: { startDate: Date | null, endDate: Date | null }) {
    this.dateRange = range;
    // Don't load orders immediately - wait for Apply button click
  }

  /**
   * Format a date to YYYY-MM-DD format
   */
  formatDate(date: Date): string {
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  toggleFilters() {
    this.showFilters = !this.showFilters;
  }

  toggleSelectAll() {
    this.selectAll = !this.selectAll;
    if (this.selectAll && this.orders.length > 0) {
      // Select all orders on the current page
      this.selectedOrders = this.orders.map(order => order.id);
    } else {
      // Deselect all orders
      this.selectedOrders = [];
    }
    console.log('Toggle select all:', this.selectAll, 'Selected orders:', this.selectedOrders.length);
  }

  toggleOrderSelection(orderId: number) {
    const index = this.selectedOrders.indexOf(orderId);
    if (index === -1) {
      // Add to selected orders
      this.selectedOrders.push(orderId);
    } else {
      // Remove from selected orders
      this.selectedOrders.splice(index, 1);
    }

    // Update selectAll state based on whether all orders on the current page are selected
    this.selectAll = this.orders.length > 0 && this.selectedOrders.length === this.orders.length;
    console.log('Toggle order selection:', orderId, 'Selected orders:', this.selectedOrders.length, 'All selected:', this.selectAll);
  }

  isOrderSelected(orderId: number): boolean {
    return this.selectedOrders.includes(orderId);
  }

  /**
   * Handle category selection and update services list
   */
  onCategorySelected(category: IconBaseModel): void {
    console.log('Category selected:', category);

    // Handle "All Categories" option
    if (category.id === 'all') {
      // For "All Categories", display all services from all categories
      this.services = [
        // Add an "All Services" option at the top using the factory method
        SuperGeneralSvRes.create({
          id: -1,
          name: 'filter.all_services',
          description: '',
          price: 0
        }),
        ...this.allServices
      ];
      this.selectedCategoryId = null;
      console.log('All services loaded, count:', this.services.length);
    } else {
      this.selectedCategoryId = parseInt(category.id);

      // Find the selected category in platforms data and get its services
      for (const platform of this.platforms) {
        const foundCategory = platform.categories.find(c => c.id.toString() === category.id);
        if (foundCategory) {
          // Map SuperGeneralSvRes to GService
          this.services = [
            // Add an "All Services" option at the top using the factory method
            SuperGeneralSvRes.create({
              id: -1,
              name: 'filter.all_services',
              description: '',
              price: 0
            }),
            // Add category services
            ...foundCategory.services
          ];
          console.log('Services loaded for category:', category.label, 'Services count:', this.services.length);
          break;
        }
      }
    }

    // Reset selected service when category changes
    this.selectedServiceId = null;

    // Don't load orders immediately - wait for Apply button click
  }

  /**
   * Handle service selection
   */
  onServiceSelected(service: SuperGeneralSvRes): void {
    console.log('Service selected:', service);

    if (service.id === -1) {
      this.selectedServiceId = null;
    } else {
      this.selectedServiceId = service.id;
    }

    // Don't load orders immediately - wait for Apply button click
  }

  handleDocumentClick(event: MouseEvent): void {
    // If we have an active menu and the click is not on a button with the ellipsis icon
    // and not on the menu itself
    if (this.activeActionMenu !== null &&
        !(event.target as HTMLElement).closest('.action-menu-button') &&
        !(event.target as HTMLElement).closest('.dropdown-menu')) {
      this.activeActionMenu = null;
    }

    // Close bulk action menu when clicking outside
    if (this.showBulkActionMenu &&
        !(event.target as HTMLElement).closest('.bulk-action-button') &&
        !(event.target as HTMLElement).closest('.bulk-action-menu')) {
      this.showBulkActionMenu = false;
    }
  }

  openEditLinkModal(order: OrderRes) {
    this.selectedOrderId = order.id;
    this.selectedOrderLink = order.link;
    this.showEditLinkModal = true;
  }

  closeEditLinkModal() {
    this.showEditLinkModal = false;
    this.selectedOrderId = null;
    this.selectedOrderLink = '';
  }

  saveEditedLink(link: string) {
    if (this.selectedOrderId) {
      this.orderService.updateOrderLink(this.selectedOrderId, link).subscribe({
        next: () => {
          this.toastService.showSuccess('Order link updated successfully');
        },
        error: (error) => {
          console.error('Error updating order link:', error);
          this.toastService.showError(error?.message || 'Failed to update order link');
        }
      });
    }
    this.closeEditLinkModal();
  }

  openSetCountModal(order: OrderRes) {
    this.selectedOrderId = order.id;
    this.selectedOrderStartCount = order.start_count || 0;
    this.showSetCountModal = true;
  }

  closeSetCountModal() {
    this.showSetCountModal = false;
    this.selectedOrderId = null;
    this.selectedOrderStartCount = 0;
  }

  saveStartCount(count: number) {
    if (this.selectedOrderId) {
      this.orderService.updateOrderStartCount(this.selectedOrderId, count).subscribe({
        next: () => {
          this.toastService.showSuccess('Start count updated successfully');
        },
        error: (error) => {
          console.error('Error updating start count:', error);
          this.toastService.showError(error?.message || 'Failed to update start count');
        }
      });
    }
    this.closeSetCountModal();
  }

  openSetPartialModal(order: OrderRes) {
    this.selectedOrderId = order.id;
    this.selectedOrderQuantity = order.quantity || 0;
    this.showSetPartialModal = true;
    // Close the action menu
    this.activeActionMenu = null;
  }

  closeSetPartialModal() {
    this.showSetPartialModal = false;
    this.selectedOrderId = null;
    this.selectedOrderQuantity = 0;
  }



  onPartialUpdated(remains: number) {
    console.log('Partial updated with remains:', remains);
    this.toastService.showSuccess('Order marked as partial successfully');
    // Refresh the orders list to show the updated status
    this.loadOrders(this.pagination.pageNumber);
    this.closeSetPartialModal();
  }



  onLinkUpdated(link: string) {
    this.saveEditedLink(link);
  }

  onCountUpdated(count: number) {
    this.saveStartCount(count);
  }

  toggleFilter(filter: any) {
    this.onStatusFilterClick(filter);
  }

  /**
   * Apply all selected filters and load orders
   */
  applyFilters() {
    console.log('Applying filters - Category ID:', this.selectedCategoryId, 'Service ID:', this.selectedServiceId);

    // Apply user ID filter if input is provided
    if (this.userIdInput) {
      this.selectedUserId = Number(this.userIdInput);
    } else {
      this.selectedUserId = null;
    }

    this.loadOrders();
  }

  resetFilters() {
    this.selectedStatus = 'all';
    this.statusFilters.forEach(filter => {
      filter.active = filter.value === 'all';
    });
    this.selectedCategoryId = null;
    this.selectedServiceId = null;
    this.selectedUserId = null;
    this.userIdInput = '';
    this.dateRange = {
      startDate: null,
      endDate: null
    };
    this.loadOrders();
  }

  /**
   * Get menu items for an order
   */
  getOrderMenuItems(order: OrderRes): AdminMenuItem[] {
    const items: AdminMenuItem[] = [];
    const status = order.status.toLowerCase();

    // Status transition rules:
    // in_progress & pending: Can change to → Completed, Partial, Canceled
    // completed: Can change to → In progress, Partial, Canceled
    // canceled & failed: Cannot change status (no menu items)
    // partial: Can change to → In progress, Partial, Canceled

    // For canceled and failed orders - no status change options
    if (['canceled', 'failed', 'canceled_without_refund'].includes(status)) {
      // Only show non-status-change actions
      items.push({
        id: 'set-start-count',
        label: 'Set start count',
        icon: 'clock',
        iconColor: 'text-blue-500'
      });

      items.push({
        id: 'edit-link',
        label: 'Edit order link',
        icon: 'edit',
        iconColor: 'text-blue-500'
      });

      return items;
    }

    // Status change options based on current status
    if (['in_progress', 'pending'].includes(status)) {
      // Can change to: Completed, Partial, Partial without refund, Canceled
      items.push({
        id: 'mark-completed',
        label: 'Mark as completed',
        icon: 'check',
        iconColor: 'text-green-500'
      });

      items.push({
        id: 'mark-partial',
        label: 'Mark as partial with refund',
        icon: 'minus-circle',
        iconColor: 'text-orange-500'
      });

      items.push({
        id: 'mark-canceled-without-refund',
        label: 'Mark as canceled (no refund)',
        icon: 'times-circle',
        iconColor: 'text-purple-500'
      });

      items.push({
        id: 'mark-canceled',
        label: 'Mark as canceled with refund',
        icon: 'times',
        iconColor: 'text-red-500'
      });
    } else if (status === 'completed') {
      // Can change to: In progress, Partial, Partial without refund, Canceled
      items.push({
        id: 'mark-in-progress',
        label: 'Mark as in progress',
        icon: 'play',
        iconColor: 'text-blue-500'
      });

      items.push({
        id: 'mark-partial',
        label: 'Mark as partial with refund',
        icon: 'minus-circle',
        iconColor: 'text-orange-500'
      });
    items.push({
        id: 'mark-canceled',
        label: 'Mark as canceled',
        icon: 'times',
        iconColor: 'text-red-500'
      });

      items.push({
        id: 'mark-canceled-without-refund',
        label: 'Mark as canceled without refund',
        icon: 'times',
        iconColor: 'text-purple-500'
      });
    } else if (status === 'partial') {
      // Can change to: In progress, Partial, Partial without refund, Canceled
      items.push({
        id: 'mark-in-progress',
        label: 'Mark as in progress',
        icon: 'play',
        iconColor: 'text-blue-500'
      });

      items.push({
        id: 'mark-partial',
        label: 'Mark as partial with refund',
        icon: 'minus-circle',
        iconColor: 'text-orange-500'
      });


      items.push({
        id: 'mark-canceled',
        label: 'Mark as canceled with refund',
        icon: 'times',
        iconColor: 'text-red-500'
      });
     items.push({
        id: 'mark-canceled-without-refund',
        label: 'Mark as canceled without refund',
        icon: 'times',
        iconColor: 'text-purple-500'
      });
    }




    // Common actions for all changeable statuses
    items.push({
      id: 'set-start-count',
      label: 'Set start count',
      icon: 'clock',
      iconColor: 'text-blue-500'
    });

    items.push({
      id: 'edit-link',
      label: 'Edit order link',
      icon: 'edit',
      iconColor: 'text-blue-500'
    });

    return items;
  }

  /**
   * Handle order menu item click
   */
  onOrderMenuItemClick(itemId: string, order: OrderRes): void {
    switch (itemId) {
      case 'mark-completed':
        this.markAsCompleted(order);
        break;
      case 'mark-in-progress':
        this.markAsInProgress(order);
        break;
      case 'mark-partial':
        this.markAsPartial(order);
        break;
      case 'mark-canceled-without-refund':
        this.markAsCanceledWithoutRefund(order);
        break;
      case 'mark-canceled':
        this.markAsCanceled(order);
        break;
      case 'set-start-count':
        this.setStartCount(order);
        break;
      case 'edit-link':
        this.editOrderLink(order);
        break;
      case 'cancel-refund':
        this.cancelWithRefund(order);
        break;
    }
  }

  /**
   * Get menu items for bulk actions
   */
  getBulkActionMenuItems(): AdminMenuItem[] {
    const items: AdminMenuItem[] = [];

    // Copy IDs - Always available
    items.push({
      id: 'copy-ids',
      label: 'Copy IDs',
      icon: 'copy',
      iconColor: 'text-gray-500'
    });

    // Copy provider IDs - Always available
    items.push({
      id: 'copy-provider-ids',
      label: 'Copy provider ids',
      icon: 'copy',
      iconColor: 'text-gray-500'
    });

    // Resend to provider - Show if any selected orders are CANCELED, PARTIAL, or PARTIAL_WITHOUT_REFUND
    if (this.hasOrdersWithStatus(['canceled', 'partial', 'partial_without_refund'])) {
      items.push({
        id: 'bulk-resend-provider',
        label: 'Resend to provider',
        icon: 'play-circle',
        iconColor: 'text-blue-500'
      });
    }

    // Mark as completed - Show if any selected orders are CANCELED, IN_PROGRESS, PARTIAL, PARTIAL_WITHOUT_REFUND, or PENDING
    if (this.hasOrdersWithStatus(['canceled', 'in_progress', 'partial', 'partial_without_refund', 'pending'])) {
      items.push({
        id: 'bulk-mark-completed',
        label: 'Mark as completed',
        icon: 'check',
        iconColor: 'text-green-500'
      });
    }

    // Cancel with refund - Show if any selected orders are IN_PROGRESS or PENDING
    if (this.hasOrdersWithStatus(['in_progress', 'pending'])) {
      items.push({
        id: 'bulk-cancel-refund',
        label: 'Cancel with refund',
        icon: 'times',
        iconColor: 'text-red-500'
      });
    }

    return items;
  }

  /**
   * Handle bulk action menu item click
   */
  onBulkActionMenuItemClick(itemId: string): void {
    switch (itemId) {
      case 'copy-ids':
        this.CopyID();
        break;
      case 'copy-provider-ids':
        this.copyProviderIds();
        break;
      case 'bulk-resend-provider':
        this.bulkResendToProvider();
        break;
      case 'bulk-mark-completed':
        this.bulkMarkAsCompleted();
        break;
      case 'bulk-cancel-refund':
        this.bulkCancelWithRefund();
        break;
    }
  }

  /**
   * Apply user ID filter from input
   * Automatically filters when input changes
   */
  applyUserIdFilter() {
    // If input is empty or null, clear the filter
    if (!this.userIdInput) {
      this.selectedUserId = null;
    } else {
      this.selectedUserId = Number(this.userIdInput);
    }
    this.loadOrders();
  }

  /**
   * Copy text to clipboard
   * @param text Text to copy
   */
  copyToClipboard(text: string) {
    if (text) {
      this.clipboard.copy(text);
      this.toastService.showSuccess('Copied to clipboard');
    }
  }

  CopyID() {
    if (this.orders.length === 0) {
      console.log('No orders to copy');
      return;
    }

    // If there are selected orders, copy only those, otherwise copy all orders
    const ordersToCopy = this.selectedOrders.length > 0
      ? this.orders.filter(order => this.selectedOrders.includes(order.id))
      : this.orders;

    const formattedOrders = ordersToCopy.map(order => this.formatOrderForCopy(order)).join('\n');

    this.clipboard.copy(formattedOrders);
    console.log('Orders copied to clipboard');
    this.toastService.showSuccess('Orders copied to clipboard successfully');
    this.showBulkActionMenu = false;
  }

  /**
   * Format a single order for copying
   * Format: ID: 20 | 5/26/25, 1:11 PM
   * 1019 - Facebook post like vietnam | cheap | speed 1-2k / day | cancel button - 0.76922 $
   * https://www.facebook.com/DuySexy/posts/pfbid02DCvPD3aZFw6Z3KPgGgYD9CirK2r2UVTa866shbAgQtoyZi8zAcP265cf3BH66Bobl
   */
  formatOrderForCopy(order: OrderRes): string {
    const formattedDate = new Date(order.created_at).toLocaleString('en-US', {
      month: 'numeric',
      day: 'numeric',
      year: '2-digit',
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    });

    const price = this.currencyService.formatPrice(order.actual_charge || order.charge);

    return `ID: ${order.id} | ${formattedDate}\n${order.service.id} - ${order.service.name} - ${price}\n${order.link}`;
  }

  /**
   * Copy a single order with the same format
   */
  copySingleOrder(order: OrderRes): void {
    const formattedOrder = this.formatOrderForCopy(order);

    this.clipboard.copy(formattedOrder);
    console.log('Order copied to clipboard');
    this.toastService.showSuccess('Order copied to clipboard successfully');
  }

  copyProviderIds() {
    const selectedOrders = this.orders.filter(order => this.selectedOrders.includes(order.id));
    const providerIds = selectedOrders.map(order => order.api_order_id || '').filter(id => id).join('\n');

    if (providerIds) {
      this.clipboard.copy(providerIds);
      console.log('Provider IDs copied to clipboard');
      this.toastService.showSuccess('Provider IDs copied to clipboard');
    }
    this.showBulkActionMenu = false;
  }

  bulkResendToProvider() {
    console.log('Bulk resend to provider for orders:', this.selectedOrders);
    // Implement the actual functionality here
    this.showBulkActionMenu = false;
  }

  bulkMarkAsCompleted() {
    console.log('Bulk mark as completed for orders:', this.selectedOrders);

    if (this.selectedOrders.length === 0) {
      this.toastService.showWarning('No orders selected');
      this.showBulkActionMenu = false;
      return;
    }

    // Get the selected orders that can be marked as completed
    const eligibleOrders = this.orders.filter(order =>
      this.selectedOrders.includes(order.id) &&
      ['canceled', 'in_progress', 'partial', 'partial_without_refund', 'pending'].includes(order.status.toLowerCase())
    );

    if (eligibleOrders.length === 0) {
      this.toastService.showWarning('No eligible orders to mark as completed');
      this.showBulkActionMenu = false;
      return;
    }

    // Show loading indicator
    this.isLoading = true;
    this.loadingService.show('Processing orders...');

    // Track completion status
    let completedCount = 0;
    let errorCount = 0;

    // Process each order

    // Process orders one by one
    const processNextOrder = (index: number) => {
      if (index >= eligibleOrders.length) {
        // All orders processed
        this.isLoading = false;
        this.loadingService.hide();
        this.toastService.showSuccess(`Marked ${completedCount} orders as completed (${errorCount} errors)`);

        // Clear selection
        this.selectedOrders = [];
        this.selectAll = false;

        // Refresh the orders list
        this.loadOrders(this.pagination.pageNumber);
        return;
      }

      const order = eligibleOrders[index];
      this.orderService.updateOrderStatusWithRemains(order.id, 'COMPLETED').subscribe({
        next: (updatedOrder) => {
          completedCount++;

          // Update the order in the local array
          const orderIndex = this.orders.findIndex(o => o.id === order.id);
          if (orderIndex !== -1) {
            this.orders[orderIndex] = updatedOrder;
          }

          processNextOrder(index + 1);
        },
        error: (error) => {
          console.error(`Error marking order ${order.id} as completed:`, error);
          this.toastService.showError(error?.message || `Failed to mark order ${order.id} as completed`);
          errorCount++;
          processNextOrder(index + 1);
        }
      });
    };

    // Start processing
    processNextOrder(0);

    this.showBulkActionMenu = false;
  }

  bulkCancelWithRefund() {
    console.log('Bulk cancel with refund for orders:', this.selectedOrders);

    if (this.selectedOrders.length === 0) {
      this.toastService.showWarning('No orders selected');
      this.showBulkActionMenu = false;
      return;
    }

    // Get the selected orders that can be cancelled with refund (IN_PROGRESS or PENDING)
    const eligibleOrders = this.orders.filter(order =>
      this.selectedOrders.includes(order.id) &&
      ['in_progress', 'pending'].includes(order.status.toLowerCase())
    );

    if (eligibleOrders.length === 0) {
      this.toastService.showWarning('No eligible orders to cancel with refund');
      this.showBulkActionMenu = false;
      return;
    }

    // Show loading indicator
    this.isLoading = true;
    this.loadingService.show('Processing orders...');

    // Track completion status
    let completedCount = 0;
    let errorCount = 0;

    // Process orders one by one
    const processNextOrder = (index: number) => {
      if (index >= eligibleOrders.length) {
        // All orders processed
        this.isLoading = false;
        this.loadingService.hide();
        this.toastService.showSuccess(`Cancelled ${completedCount} orders with refund (${errorCount} errors)`);

        // Clear selection
        this.selectedOrders = [];
        this.selectAll = false;

        // Refresh the orders list
        this.loadOrders(this.pagination.pageNumber);
        return;
      }

      const order = eligibleOrders[index];
      this.orderService.cancelOrder(order.id).subscribe({
        next: (updatedOrder) => {
          completedCount++;

          // Update the order in the local array
          const orderIndex = this.orders.findIndex(o => o.id === order.id);
          if (orderIndex !== -1) {
            this.orders[orderIndex] = updatedOrder;
          }

          processNextOrder(index + 1);
        },
        error: (error) => {
          console.error(`Error cancelling order ${order.id} with refund:`, error);
          this.toastService.showError(error?.message || `Failed to cancel order ${order.id} with refund`);
          errorCount++;
          processNextOrder(index + 1);
        }
      });
    };

    // Start processing
    processNextOrder(0);

    this.showBulkActionMenu = false;
  }

  toggleBulkActionMenu(event: MouseEvent) {
    this.showBulkActionMenu = !this.showBulkActionMenu;

    // Close any other open action menu
    this.activeActionMenu = null;

    // Position the bulk action menu
    if (event && this.showBulkActionMenu) {
      const button = event.currentTarget as HTMLElement;
      const rect = button.getBoundingClientRect();

      // Check if the menu would be cut off at the bottom or right of the viewport
      const menuHeight = 200; // Approximate max height of the bulk action menu
      const menuWidth = 224; // Width of the menu (56 * 4 = 224px)
      const viewportHeight = window.innerHeight;
      const viewportWidth = window.innerWidth;
      const spaceBelow = viewportHeight - rect.bottom;
      const spaceRight = viewportWidth - rect.left;

      // Determine vertical position (top)
      let topPosition = 0;
      if (spaceBelow < menuHeight) {
        // Position above if not enough space below
        topPosition = rect.top + window.scrollY - menuHeight;
      } else {
        // Position below if there's enough space
        topPosition = rect.bottom + window.scrollY;
      }

      // Determine horizontal position (left)
      let leftPosition = 0;

      if (spaceRight < menuWidth) {
        // Align to the right edge of the button if not enough space to the right
        leftPosition = rect.right + window.scrollX - menuWidth;
      } else {
        // Align to the left edge of the button if there's enough space
        leftPosition = rect.left + window.scrollX;
      }

      this.bulkMenuPosition = {
        top: topPosition,
        left: leftPosition
      };
    }

    // Stop event propagation to prevent document click handler from closing the menu immediately
    if (event) {
      event.stopPropagation();
    }
  }

  /**
   * Toggle provider details visibility in mobile view
   */
  toggleProviderDetails(orderId: number): void {
    this.showProviderDetails[orderId] = !this.showProviderDetails[orderId];
  }

  /**
   * Show tooltip for a specific order
   */
  showTooltip(orderId: number, event: Event): void {
    event.stopPropagation();

    // Don't show tooltips on mobile devices to avoid scroll issues
    if (window.innerWidth < 768) {
      return;
    }

    // Clear any existing timeout
    if (this.tooltipTimeout) {
      clearTimeout(this.tooltipTimeout);
      this.tooltipTimeout = null;
    }

    // Store the trigger element for position updates
    const target = event.target as HTMLElement;
    this.tooltipTriggerElement = target;

    // Calculate tooltip position using the new method
    this.calculateTooltipPosition(target, 'main');
    this.activeTooltip = orderId;
  }

  /**
   * Hide tooltip with a delay
   */
  hideTooltip(): void {
    // Set a timeout to hide the tooltip after a short delay
    // This gives time for the mouse to move between elements
    this.tooltipTimeout = setTimeout(() => {
      this.activeTooltip = null;
      this.tooltipTriggerElement = null;
    }, 300); // 300ms delay
  }

  /**
   * Prevent tooltip from closing when mouse enters it
   */
  keepTooltipOpen(event: Event): void {
    event.stopPropagation();
    // Clear any hide timeout when mouse enters the tooltip
    if (this.tooltipTimeout) {
      clearTimeout(this.tooltipTimeout);
      this.tooltipTimeout = null;
    }
  }

  /**
   * Show note tooltip for a specific order
   */
  showNoteTooltip(orderId: number, event: Event, note: string): void {
    event.stopPropagation();

    // Don't show tooltips on mobile devices to avoid scroll issues
    if (window.innerWidth < 768) {
      return;
    }

    // Clear any existing timeout
    if (this.noteTooltipTimeout) {
      clearTimeout(this.noteTooltipTimeout);
      this.noteTooltipTimeout = null;
    }

    // Store the trigger element for position updates
    const target = event.target as HTMLElement;
    this.noteTooltipTriggerElement = target;

    // Calculate tooltip position using the new method
    this.calculateTooltipPosition(target, 'note');
    this.activeNoteTooltip = orderId;
  }

  /**
   * Hide note tooltip with a delay
   */
  hideNoteTooltip(): void {
    // Set a timeout to hide the tooltip after a short delay
    // This gives time for the mouse to move between elements
    this.noteTooltipTimeout = setTimeout(() => {
      this.activeNoteTooltip = null;
      this.noteTooltipTriggerElement = null;
    }, 300); // 300ms delay
  }

  /**
   * Prevent note tooltip from closing when mouse enters it
   */
  keepNoteTooltipOpen(event: Event): void {
    event.stopPropagation();
    // Clear any hide timeout when mouse enters the tooltip
    if (this.noteTooltipTimeout) {
      clearTimeout(this.noteTooltipTimeout);
      this.noteTooltipTimeout = null;
    }
  }

  /**
   * Get CSS class for status badge
   */
  getStatusClass(status: string): string {
    switch (status.toLowerCase()) {
      case 'completed':
      case 'hoàn thành':
      case 'thành công':
        return 'bg-green-100 text-green-800';
      case 'pending':
      case 'đang chờ':
      case 'chờ xử lý':
        return 'bg-blue-100 text-blue-800';
      case 'in_progress':
      case 'đang chạy':
      case 'đang xử lý':
        return 'bg-yellow-100 text-yellow-800';
      case 'partial':
      case 'chạy 1 phần':
      case 'một phần':
        return 'bg-orange-100 text-orange-800';
      case 'canceled_without_refund':
      case 'hủy (không hoàn tiền)':
        return 'bg-purple-100 text-purple-800';
      case 'canceled':
      case 'hủy':
      case 'đã hủy':
        return 'bg-red-100 text-red-800';
      case 'failed':
      case 'thất bại':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  }

  // Method removed to avoid duplication with CopyID()

  toggleActionMenu(orderId: number, event?: MouseEvent): void {
    // Prevent event propagation to avoid immediate closing by document click
    if (event) {
      event.stopPropagation();
    }

    if (this.activeActionMenu === orderId) {
      this.activeActionMenu = null;
    } else {
      this.activeActionMenu = orderId;

      // Position the menu relative to the button
      if (event) {
        const button = event.currentTarget as HTMLElement;
        const rect = button.getBoundingClientRect();

        // Check if the menu would be cut off at the bottom or right of the viewport
        const menuHeight = 300; // Approximate max height of the menu
        const menuWidth = 224; // Width of the menu (56 * 4 = 224px)
        const viewportHeight = window.innerHeight;
        const viewportWidth = window.innerWidth;
        const spaceBelow = viewportHeight - rect.bottom;
        const spaceRight = viewportWidth - rect.left;

        // Determine vertical position (top)
        let topPosition = 0;
        if (spaceBelow < menuHeight) {
          // Position above if not enough space below
          topPosition = rect.top + window.scrollY - menuHeight;
        } else {
          // Position below if there's enough space
          topPosition = rect.bottom + window.scrollY;
        }

        // Determine horizontal position (left/right)
        let leftPosition = 0;
        let rightPosition = 0;

        if (spaceRight < menuWidth) {
          // Align to the right edge of the button if not enough space to the right
          leftPosition = rect.right + window.scrollX - menuWidth;
          rightPosition = window.innerWidth - rect.right - window.scrollX;
        } else {
          // Align to the left edge of the button if there's enough space
          leftPosition = rect.left + window.scrollX;
          rightPosition = window.innerWidth - rect.right - window.scrollX;
        }

        this.menuPosition = {
          top: topPosition,
          left: leftPosition,
          right: rightPosition
        };
      }
    }
  }

  markAsCompleted(order: OrderRes): void {
    console.log('Mark as completed:', order.id);

    // Call the API to update the order status to COMPLETED
    this.orderService.updateOrderStatusWithRemains(order.id, 'COMPLETED').subscribe({
      next: (updatedOrder) => {
        console.log('Order marked as completed:', updatedOrder);
        this.toastService.showSuccess('Order marked as completed successfully');

        // Close the action menu
        this.activeActionMenu = null;

        // Refresh the orders list to show the updated status
        this.loadOrders(this.pagination.pageNumber);
      },
      error: (error) => {
        console.error('Error marking order as completed:', error);
        this.toastService.showError(error?.message || 'Failed to mark order as completed');
      }
    });
  }

  markAsInProgress(order: OrderRes): void {
    console.log('Mark as in progress:', order.id);

    // Call the API to update the order status to IN_PROGRESS
    this.orderService.updateOrderStatusWithRemains(order.id, 'IN_PROGRESS').subscribe({
      next: (updatedOrder) => {
        console.log('Order marked as in progress:', updatedOrder);
        this.toastService.showSuccess('Order marked as in progress successfully');

        // Close the action menu
        this.activeActionMenu = null;

        // Refresh the orders list to show the updated status
        this.loadOrders(this.pagination.pageNumber);
      },
      error: (error) => {
        console.error('Error marking order as in progress:', error);
        this.toastService.showError(error?.message || 'Failed to mark order as in progress');
      }
    });
  }

  markAsPartial(order: OrderRes): void {
    console.log('Mark as partial:', order.id);
    this.openSetPartialModal(order);
  }

  markAsCanceledWithoutRefund(order: OrderRes): void {
    console.log('Mark as canceled without refund:', order.id);

    // Call the API to update the order status to CANCELED_WITHOUT_REFUND
    this.orderService.updateOrderStatusWithRemains(order.id, 'CANCELED_WITHOUT_REFUND').subscribe({
      next: (updatedOrder) => {
        console.log('Order marked as canceled without refund:', updatedOrder);
        this.toastService.showSuccess('Order marked as canceled without refund successfully');

        // Close the action menu
        this.activeActionMenu = null;

        // Refresh the orders list to show the updated status
        this.loadOrders(this.pagination.pageNumber);
      },
      error: (error) => {
        console.error('Error marking order as canceled without refund:', error);
        this.toastService.showError(error?.message || 'Failed to mark order as canceled without refund');
      }
    });
  }

  markAsCanceled(order: OrderRes): void {
    console.log('Mark as canceled:', order.id);

    // Call the API to update the order status to CANCELED
    this.orderService.updateOrderStatusWithRemains(order.id, 'CANCELED').subscribe({
      next: (updatedOrder) => {
        console.log('Order marked as canceled:', updatedOrder);
        this.toastService.showSuccess('Order marked as canceled successfully');

        // Close the action menu
        this.activeActionMenu = null;

        // Refresh the orders list to show the updated status
        this.loadOrders(this.pagination.pageNumber);
      },
      error: (error) => {
        console.error('Error marking order as canceled:', error);
        this.toastService.showError(error?.message || 'Failed to mark order as canceled');
      }
    });
  }

  resendToProvider(order: OrderRes): void {
    console.log('Resend to provider:', order.id);
    // Implement the actual functionality here
  }

  setStartCount(order: OrderRes): void {
    this.openSetCountModal(order);
  }

  editOrderLink(order: OrderRes): void {
    this.openEditLinkModal(order);
  }

  cancelPartially(order: OrderRes): void {
    console.log('Cancel partially:', order.id);
    // Implement the actual functionality here
  }

  cancelWithRefund(order: OrderRes): void {
    console.log('Cancel with refund:', order.id);

    // Call the API to cancel the order with refund
    this.orderService.cancelOrder(order.id).subscribe({
      next: (updatedOrder) => {
        console.log('Order cancelled with refund:', updatedOrder);
        this.toastService.showSuccess('Order cancelled with refund successfully');

        // Close the action menu
        this.activeActionMenu = null;

        // Refresh the orders list to show the updated status
        this.loadOrders(this.pagination.pageNumber);
      },
      error: (error) => {
        console.error('Error cancelling order with refund:', error);
        this.toastService.showError(error.message || 'Failed to cancel order with refund');
      }
    });
  }

  /**
   * Navigate to a specific page
   * @param page The page number (0-based)
   */
  goToPage(page: number): void {
    if (page < 0 || page >= this.pagination.totalPages) {
      return;
    }

    // Update the current page in the service
    this.loadOrders(page);
  }

  /**
   * Change the page size and reload orders
   */
  changePageSize(): void {
    // Reset to first page when changing page size
    this.loadOrders(0);
  }

  /**
   * Get the range of pages to display in pagination
   * Shows a window of pages around the current page
   */
  getPageRange(): number[] {
    const totalPages = this.pagination.totalPages;
    const currentPage = this.pagination.pageNumber;

    // If we have 7 or fewer pages, show all pages
    if (totalPages <= 7) {
      return Array.from({ length: totalPages }, (_, i) => i);
    }

    // Otherwise, show a window around the current page
    let startPage = Math.max(1, currentPage - 1);
    let endPage = Math.min(totalPages - 2, currentPage + 1);

    // Adjust the window if we're near the beginning or end
    if (currentPage <= 2) {
      endPage = 3;
    } else if (currentPage >= totalPages - 3) {
      startPage = totalPages - 4;
    }

    return Array.from(
      { length: endPage - startPage + 1 },
      (_, i) => startPage + i
    );
  }

  /**
   * Check if any of the selected orders have one of the specified statuses
   * @param statuses Array of status strings to check for (lowercase)
   * @returns true if any selected order has one of the specified statuses
   */
  hasOrdersWithStatus(statuses: string[]): boolean {
    if (!this.selectedOrders || this.selectedOrders.length === 0) {
      return false;
    }

    // Get the selected order objects
    const selectedOrderObjects = this.orders.filter(order =>
      this.selectedOrders.includes(order.id)
    );

    // Check if any of the selected orders have one of the specified statuses
    return selectedOrderObjects.some(order =>
      statuses.includes(order.status.toLowerCase())
    );
  }

  /**
   * Format balance using the common currency service
   */
  formatBalance(balance: number | undefined): string {
    return this.currencyService.formatBalance(balance);
  }
}
