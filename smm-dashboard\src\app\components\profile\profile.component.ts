import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, HostListener } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { IconsModule } from '../../icons/icons.module';
import { UserService } from '../../core/services/user.service';
import { UserRes } from '../../model/response/user-res.model';
import { ToastService } from '../../core/services/toast.service';
import { LiteDropdownComponent } from '../common/lite-dropdown/lite-dropdown.component';
import { LanguageService } from '../../core/services/language.service';
import { TenantSettingsService } from '../../core/services/tenant-settings.service';
import { TenantCurrencyService } from '../../core/services/tenant-currency.service';
import { LoginHistoryItem } from '../../model/response/login-history.model';
import { Subscription } from 'rxjs';
import { take } from 'rxjs/operators';
// ToggleSwitchComponent removed as we're using buttons instead
import { ActivatedRoute } from '@angular/router';
import { CurrencyReq } from '../../model/request/currency-req.model';
import { EditUserReq } from '../../model/request/edit-user-req.model';
import { CurrencyConvertPipe } from "../../core/pipes/currency-convert.pipe";
import { CurrencyService } from '../../core/services/currency.service';
import { MfaSettingComponent } from '../popup/mfa-setting/mfa-setting.component';
import { MfaDisabledComponent } from '../popup/mfa-disabled/mfa-disabled.component';

@Component({
  selector: 'app-profile',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    TranslateModule,
    IconsModule,
    LiteDropdownComponent,
    MfaSettingComponent,
    MfaDisabledComponent
],
  templateUrl: './profile.component.html',
  styleUrls: ['./profile.component.css', './online-dot.css']
})
export class ProfileComponent implements OnInit, OnDestroy {
  profileForm: FormGroup;
  passwordForm: FormGroup;
  user: UserRes | undefined;
  isLoading = false;
  isPasswordLoading = false;
  isVerifying = false;
  loginHistory: LoginHistoryItem[] = [];
  isLoadingHistory = false;
  private subscriptions: Subscription[] = [];
  passwordError: string | null = null;
  profileError: string | null = null;

  // Tab navigation
  activeTab: 'account' | 'security' | 'settings' | 'history' = 'account';

  // Password visibility toggles
  showCurrentPassword = false;
  showNewPassword = false;
  showConfirmPassword = false;

  // Pagination for login history
  currentPage = 1;
  totalPages = 1;
  itemsPerPage = 10;

  // Security options
  is2FAEnabled = false;

  // Settings options
  allLanguages = [
    { flag: 'fi fi-vn', code: 'vi', name: 'Tiếng Việt' },
    { flag: 'fi fi-us', code: 'en', name: 'English' },
    { flag: 'fi fi-ru', code: 'ru', name: 'Русский' },
    { flag: 'fi fi-ua', code: 'uk', name: 'Українська' },
    { flag: 'fi fi-de', code: 'de', name: 'Deutsch' },
    { flag: 'fi fi-tr', code: 'tr', name: 'Türkçe' },
    { flag: 'fi fi-fr', code: 'fr', name: 'Français' },
    { flag: 'fi fi-es', code: 'es', name: 'Español' },
    { flag: 'fi fi-pt', code: 'pt', name: 'Português' },
    { flag: 'fi fi-it', code: 'it', name: 'Italiano' },
    { flag: 'fi fi-jp', code: 'ja', name: '日本語' },
    { flag: 'fi fi-kr', code: 'ko', name: '한국어' },
    { flag: 'fi fi-cn', code: 'cn', name: '中文' },
    { flag: 'fi fi-sa', code: 'ar', name: 'العربية' },
    { flag: 'fi fi-in', code: 'hi', name: 'हिन्दी' },
    { flag: 'fi fi-th', code: 'th', name: 'ไทย' },
    { flag: 'fi fi-id', code: 'id', name: 'Bahasa Indonesia' },
    { flag: 'fi fi-my', code: 'ms', name: 'Bahasa Melayu' },
    { flag: 'fi fi-pl', code: 'pl', name: 'Polski' },
    { flag: 'fi fi-nl', code: 'nl', name: 'Nederlands' }
  ];
  languageOptions: string[] = [];
  availableLanguages: any[] = []; // Available languages with flag info
  selectedLanguage = 'English';
  showLanguageDropdown = false;

  currencyOptions: string[] = [];
  selectedCurrency = 'USD (United States Dollar)';

  timezoneOptions = ['(UTC +7:00) Krasnoyarsk Time, Cambodia, Laos, Thailand, Vietnam', '(UTC +8:00) Beijing, Hong Kong, Singapore'];
  selectedTimezone = '(UTC +7:00) Krasnoyarsk Time, Cambodia, Laos, Thailand, Vietnam';
  formattedBalance: string = '';
  constructor(
    private fb: FormBuilder,
    private userService: UserService,
    private toast: ToastService,
    private translate: TranslateService,
    private languageService: LanguageService,
    private tenantSettingsService: TenantSettingsService,
    private tenantCurrencyService: TenantCurrencyService,
    private route: ActivatedRoute,
    private currencyService: CurrencyService
  ) {
    // Initialize profile form
    this.profileForm = this.fb.group({
      email: ['', [Validators.required, Validators.email]],
      phone: ['', [Validators.required]]
    });

    // Initialize password form
    this.passwordForm = this.fb.group({
      oldPassword: ['', [Validators.required]],
      newPassword: ['', [Validators.minLength(8)]],
      confirmPassword: ['']
    }, {
      validators: this.passwordMatchValidator
    });
  }

  ngOnInit(): void {
    // Check for tab parameter in URL query params
    this.route.queryParams.subscribe(params => {
      if (params['tab']) {
        // Check if the tab parameter is valid
        const tabParam = params['tab'];
        if (tabParam === 'account' || tabParam === 'security' ||
            tabParam === 'settings' || tabParam === 'history') {
          this.activeTab = tabParam as 'account' | 'security' | 'settings' | 'history';
        }
      }
    });

    // Subscribe to user data from the UserService - use take(1) to only get the current value
    const userSub = this.userService.user$.subscribe(user => {
      this.user = user;

      if (user) {
        // Populate form with user data
        this.profileForm.patchValue({
          email: user.email || '',
          phone: user.phone || ''
        });
        this.formattedBalance = this.currencyService.formatPrice(user.balance);
        // Set currency based on user's preferred currency
        if (user.preferred_currency) {
          // Find the matching currency option
          const currencyCode = user.preferred_currency.code;
          const matchingCurrency = this.currencyOptions.find(option =>
            option.startsWith(currencyCode)
          );

          if (matchingCurrency) {
            this.selectedCurrency = matchingCurrency;
          }
        }

        // Load login history only once
        this.loadLoginHistory();

        // Load security settings
        this.loadSecuritySettings();
      } else {
        // If user is not loaded yet, trigger a fetch
        this.userService.get$.next();
      }
    });
    this.subscriptions.push(userSub);

    // Load available languages and initialize language setting
    this.loadAvailableLanguages();

    // Load available currencies
    this.loadAvailableCurrencies();
  }

  // Load available languages from tenant settings
  private loadAvailableLanguages(): void {
    this.tenantSettingsService.getTenantAvailableLanguages().subscribe({
      next: (availableLanguages: string[]) => {
        // Filter languages based on what's available for this tenant
        const availableLanguageObjects = this.allLanguages.filter(lang =>
          availableLanguages.includes(lang.code)
        );

        // Store available languages with flag info
        this.availableLanguages = availableLanguageObjects;

        // Convert to string array for dropdown
        this.languageOptions = availableLanguageObjects.map(lang => lang.name);

        // Get current language and check if it's available
        const currentLang = this.translate.currentLang || localStorage.getItem('language') || 'vi';
        const currentLanguageObj = this.allLanguages.find(lang => lang.code === currentLang);

        if (currentLanguageObj && availableLanguages.includes(currentLang)) {
          // Current language is available, use it
          this.selectedLanguage = currentLanguageObj.name;
        } else {
          // Current language is not available, get tenant default and switch to it
          this.tenantSettingsService.getTenantDefaultLanguage().subscribe({
            next: (response: {default_language: string}) => {
              const defaultLang = response.default_language || 'vi';
              const defaultLanguageObj = this.allLanguages.find(lang => lang.code === defaultLang);

              if (defaultLanguageObj && availableLanguages.includes(defaultLang)) {
                this.selectedLanguage = defaultLanguageObj.name;
                // Switch to default language
                this.languageService.changeLanguage(defaultLang);
              } else if (availableLanguageObjects.length > 0) {
                // Fallback to first available language
                this.selectedLanguage = availableLanguageObjects[0].name;
                this.languageService.changeLanguage(availableLanguageObjects[0].code);
              }
            },
            error: (error) => {
              console.error('Error getting tenant default language:', error);
              // Fallback to first available language
              if (availableLanguageObjects.length > 0) {
                this.selectedLanguage = availableLanguageObjects[0].name;
                this.languageService.changeLanguage(availableLanguageObjects[0].code);
              }
            }
          });
        }
      },
      error: (error) => {
        console.error('Error loading available languages:', error);
        // Fallback to all languages
        this.availableLanguages = this.allLanguages;
        this.languageOptions = this.allLanguages.map(lang => lang.name);
        const currentLang = this.translate.currentLang;
        const currentLanguageObj = this.allLanguages.find(lang => lang.code === currentLang);
        this.selectedLanguage = currentLanguageObj?.name || 'English';
      }
    });
  }

  // Load available currencies from tenant settings
  private loadAvailableCurrencies(): void {
    this.tenantCurrencyService.getAvailableCurrencies().subscribe({
      next: (currencies) => {
        // Convert currencies to dropdown format
        this.currencyOptions = currencies.map(currency =>
          `${currency.code} (${currency.name})`
        );

        // Set current currency if user has one
        if (this.user?.preferred_currency) {
          const currentCurrencyCode = this.user.preferred_currency.code;
          const matchingCurrency = this.currencyOptions.find(option =>
            option.startsWith(currentCurrencyCode)
          );
          if (matchingCurrency) {
            this.selectedCurrency = matchingCurrency;
          }
        } else if (this.currencyOptions.length > 0) {
          // Default to first available currency (should be USD)
          this.selectedCurrency = this.currencyOptions[0];
        }
      },
      error: (error) => {
        console.error('Error loading available currencies:', error);
        // Fallback to default currencies
        this.currencyOptions = ['USD (United States Dollar)'];
        this.selectedCurrency = 'USD (United States Dollar)';
      }
    });
  }

  // Load security settings from user data
  loadSecuritySettings(): void {
    if (this.user) {
      // Check if 2FA is enabled (assuming the user object has this information)
      this.is2FAEnabled = this.user.mfa_enabled || false;
    }
  }

  ngOnDestroy(): void {
    // Clean up subscriptions to prevent memory leaks
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  // Load login history
  loadLoginHistory(): void {
    console.log('Loading login history...');
    this.isLoadingHistory = true;

    // Set a default empty array to prevent undefined errors
    this.loginHistory = [];

    const historySub = this.userService.getLoginHistory().subscribe({
      next: (response) => {
        console.log('Login history API response:', response);

        if (response && response.content) {
          this.loginHistory = [...response.content]; // Create a new array to trigger change detection
          console.log('Login history items:', this.loginHistory.length);

          // Add sample items for testing if the array is empty
          // if (this.loginHistory.length === 0 && response.content.length === 0) {
          //   console.log('Adding sample login history items for testing');
          //   // Sample data for testing
          //   this.loginHistory = [
          //     {
          //       id: 1,
          //       ip: '***********',
          //       user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) Chrome/*********',
          //       created_at: new Date().toISOString()
          //     },
          //     {
          //       id: 2,
          //       ip: '***********',
          //       user_agent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) Safari/605.1.15',
          //       created_at: new Date(Date.now() - 86400000).toISOString() // 1 day ago
          //     },
          //     {
          //       id: 3,
          //       ip: '***********',
          //       user_agent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_0 like Mac OS X) Mobile/15E148',
          //       created_at: new Date(Date.now() - 172800000).toISOString() // 2 days ago
          //     }
          //   ];
          // }

          // Calculate pagination
          this.totalPages = Math.ceil(this.loginHistory.length / this.itemsPerPage);
          if (this.totalPages === 0) this.totalPages = 1;
          this.currentPage = 1;
        } else {
          // If response doesn't have the expected structure, keep the empty array
          console.warn('Login history response did not have expected structure:', response);
        }

        // Log the final state for debugging
        console.log('Final login history state:', this.loginHistory);
        this.isLoadingHistory = false;
      },
      error: (error) => {
        console.error('Error loading login history:', error);
        this.loginHistory = []; // Ensure loginHistory is initialized even on error
        this.isLoadingHistory = false;
        this.toast.showError('Failed to load login history');
      }
    });

    this.subscriptions.push(historySub);
  }

  // Pagination methods
  nextPage(): void {
    if (this.currentPage < this.totalPages) {
      this.currentPage++;
    }
  }

  prevPage(): void {
    if (this.currentPage > 1) {
      this.currentPage--;
    }
  }

  get paginatedLoginHistory(): LoginHistoryItem[] {
    const startIndex = (this.currentPage - 1) * this.itemsPerPage;
    const endIndex = startIndex + this.itemsPerPage;
    return this.loginHistory.slice(startIndex, endIndex);
  }

  // Format date for display
  formatDate(dateString: string | null | undefined): string {
    if (!dateString) {
      return 'N/A';
    }
    try {
      const date = new Date(dateString);
      return date.toLocaleString();
    } catch (error) {
      console.error('Error formatting date:', error);
      return dateString;
    }
  }

  /**
   * Get the avatar image path based on user ID or a default one
   * @returns Path to the avatar image
   */
  getAvatarPath(): string {
    if (!this.user || !this.user.avatar) {
      // Default avatar if no user or no avatar
      return 'assets/images/profile-1.png';
    }

    // Return the avatar path based on user.avatar
    return `assets/images/${this.user.avatar}.png`;
  }

  // Properties for MFA popups
  showMfaSettings = false;
  showMfaDisabled = false;

  // Toggle 2FA status
  toggle2FA(): void {
    console.log('toggle2FA called, is2FAEnabled:', this.is2FAEnabled);

    if (this.is2FAEnabled) {
      // If 2FA is enabled, show the disable popup
      console.log('Showing MFA disable popup');
      this.showMfaDisabled = true;
    } else {
      // If 2FA is disabled, show the enable popup
      this.showEnableMfa();
    }
  }

  // Show enable MFA popup
  showEnableMfa(): void {
    console.log('showEnableMfa called');
    this.showMfaSettings = true;
  }

  // Show disable MFA popup
  showDisableMfa(): void {
    console.log('showDisableMfa called');
    this.showMfaDisabled = true;
  }

  // Close MFA settings popup
  closeMfaSettings(): void {
    console.log('Closing MFA settings popup');
    this.showMfaSettings = false;
  }

  // Close MFA disabled popup
  closeMfaDisabled(): void {
    console.log('Closing MFA disabled popup');
    this.showMfaDisabled = false;
  }

  // Handle MFA enabled event
  onMfaEnabled(): void {
    this.is2FAEnabled = true;
    // Update user data
    this.userService.get$.next();
  }

  // Handle MFA disabled event
  onMfaDisabled(): void {
    this.is2FAEnabled = false;
    // Update user data
    this.userService.get$.next();
  }

  // API Key related methods removed as requested

  // Settings methods
  onLanguageChange(selectedLanguageName: string): void {
    this.selectedLanguage = selectedLanguageName;

    // Find the language object by name
    const languageObj = this.allLanguages.find(lang => lang.name === selectedLanguageName);
    if (!languageObj) {
      console.error('Language not found:', selectedLanguageName);
      return;
    }

    // Change language in the app
    this.languageService.changeLanguage(languageObj.code);

    // Call API to save user language preference
    this.userService.updateUserLanguage(languageObj.code).subscribe({
      next: () => {
        this.toast.showSuccess(`Language changed to ${selectedLanguageName}`);
      },
      error: (error) => {
        console.error('Error updating user language:', error);
        this.toast.showError(error?.message || 'Failed to update language');
      }
    });
  }

  onCurrencyChange(currency: string): void {
    this.selectedCurrency = currency;

    // Extract currency code (e.g., "USD" from "USD 2.068 (United States Dollar)")
    const currencyCode = currency.split(' ')[0];

    // Create currency request object
    const currencyReq: CurrencyReq = {
      code: currencyCode
    };

    // Call API to update currency preference
    this.userService.setCurrency(currencyReq).subscribe({
      next: (updatedUser) => {
        // Update only the currency field in the user object
        if (updatedUser && this.user) {
          this.user = {
            ...this.user,
            preferred_currency: updatedUser.preferred_currency
          };
        }
        this.toast.showSuccess(`Currency changed to ${currencyCode}`);
      },
      error: (err) => {
        console.error('Error changing currency:', err);
        this.toast.showError('Failed to change currency');
      }
    });
  }

  onTimezoneChange(timezone: string): void {
    this.selectedTimezone = timezone;
    this.toast.showSuccess('Timezone updated');
  }

  // Get flag class for current selected language
  getCurrentLanguageFlag(): string {
    const languageObj = this.allLanguages.find(lang => lang.name === this.selectedLanguage);
    return languageObj ? languageObj.flag : 'fi fi-us'; // Default to US flag
  }

  // Close language dropdown when clicking outside
  @HostListener('document:click', ['$event'])
  onDocumentClick(event: MouseEvent): void {
    const target = event.target as HTMLElement;
    if (!target.closest('.setting-dropdown')) {
      this.showLanguageDropdown = false;
    }
  }

  // Custom validator to check if passwords match
  passwordMatchValidator(group: FormGroup) {
    const newPassword = group.get('newPassword')?.value;
    const confirmPassword = group.get('confirmPassword')?.value;

    if (newPassword && confirmPassword && newPassword !== confirmPassword) {
      group.get('confirmPassword')?.setErrors({ passwordMismatch: true });
      return { passwordMismatch: true };
    } else if (group.get('confirmPassword')?.hasError('passwordMismatch')) {
      // Clear the error if passwords now match
      group.get('confirmPassword')?.setErrors(null);
    }

    return null;
  }

  // Verify email
  verifyEmail(): void {
    this.isVerifying = true;
    // Implement email verification logic here
    setTimeout(() => {
      this.isVerifying = false;
      this.toast.showSuccess('Verification email sent');
    }, 1000);
  }

  // Update profile
  onSubmit(): void {
    // Reset error message
    this.profileError = null;

    if (this.profileForm.invalid) {
      // Mark all fields as touched to trigger validation messages
      Object.keys(this.profileForm.controls).forEach(key => {
        const control = this.profileForm.get(key);
        control?.markAsTouched();
      });
      return;
    }

    this.isLoading = true;

    // Update user info (email and phone)
    const userInfo: EditUserReq = {
      email: this.profileForm.value.email || '',
      phone: this.profileForm.value.phone || ''
    };

    this.userService.updateInfo(userInfo).subscribe({
      next: (updatedUser) => {
        this.isLoading = false;
        // Update only specific fields in the user object
        if (updatedUser && this.user) {
          // Only update the email and phone fields
          this.user = {
            ...this.user,
            email: updatedUser.email,
            phone: updatedUser.phone
          };
        }
        this.toast.showSuccess(this.translate.instant('profile.profile_updated'));
      },
      error: (err) => {
        this.isLoading = false;
        console.error('Profile update error:', err);

        // Set error message based on the response
        if (err && err.message) {
          this.profileError = err.message;
        } else {
          this.profileError = 'Failed to update profile. Please try again.';
        }

        this.toast.showError(this.profileError || 'Failed to update profile');
      }
    });
  }

  // Change password
  onChangePassword(): void {
    // Reset error message
    this.passwordError = null;

    if (this.passwordForm.invalid) {
      // Mark all fields as touched to trigger validation messages
      Object.keys(this.passwordForm.controls).forEach(key => {
        const control = this.passwordForm.get(key);
        control?.markAsTouched();
      });
      return;
    }

    this.isPasswordLoading = true;

    const passwordData = {
      oldPassword: this.passwordForm.value.oldPassword,
      newPassword: this.passwordForm.value.newPassword
    };

    this.userService.changePass({
      old_password: passwordData.oldPassword,
      new_password: passwordData.newPassword
    }).subscribe({
      next: () => {
        this.isPasswordLoading = false;
        this.toast.showSuccess(this.translate.instant('profile.password_updated'));
        // Reset form
        this.passwordForm.reset();
      },
      error: (err) => {
        this.isPasswordLoading = false;
        console.error('Password update error:', err);

        // Set error message based on the response
        if (err && err.message) {
          this.passwordError = err.message;
        } else {
          this.passwordError = 'Failed to update password. Please try again.';
        }

        this.toast.showError(this.passwordError || 'Failed to update password');
      }
    });
  }
}
