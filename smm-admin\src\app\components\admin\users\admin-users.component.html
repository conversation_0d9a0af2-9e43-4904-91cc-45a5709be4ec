<div class="layout-container py-6 px-6">
  <div class="mb-4">
    <h2 class="text-xl font-bold">User Management</h2>
  </div>

  <div class="mb-6">
    <div class="flex flex-wrap -mb-px">
      <div *ngFor="let filter of statusFilters" class="mr-2">
        <button (click)="toggleFilter(filter)"
          [ngClass]="{'text-[#3b82f6] border-b-2 !border-[#3b82f6] font-medium' : filter.active, 'text-gray-500 hover:border-b-2 hover:text-gray-700 hover:border-gray-300': !filter.active}"
          class="inline-block p-4 border-transparent rounded-t-lg">
          {{ filter.label }}
        </button>
      </div>
    </div>
  </div>

  <!-- Search bar -->
  <div class="mb-6 flex flex-row w-full gap-4 items-stretch h-[46px]">
    <div class="flex-grow h-full">
      <div class="relative h-full">
        <input type="text" [(ngModel)]="searchTerm" placeholder="Search by username"
          class="w-full h-full px-4 py-2 bg-white text-gray-700 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#3b82f6]">
        <button (click)="onSearch()"
          class="absolute right-0 top-0 h-full px-4 bg-[#3b82f6] text-white rounded-r-lg hover:bg-[#2563eb] transition-colors duration-200">
          <fa-icon [icon]="['fas', 'search']"></fa-icon>
        </button>
      </div>
    </div>
  </div>

  <!-- Users data display (responsive) -->
  <div>
    <!-- Desktop Table View (hidden on mobile) -->
    <div class="hidden md:block overflow-x-auto">
      <div class="inline-block min-w-full align-middle">
        <div class="overflow-hidden rounded-lg shadow-sm">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th scope="col" class="p-4">
                  <div class="flex items-center">
                    <input type="checkbox" [(ngModel)]="selectAll" (change)="toggleAllUsers()"
                      class="w-4 h-4 text-[#3b82f6] bg-white border-gray-300 rounded focus:ring-[#3b82f6]">
                  </div>
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500  tracking-wider">
                  <span *ngIf="selectedUsers.length === 0">ID</span>
                  <app-admin-menu *ngIf="selectedUsers.length > 0"
                    [menuItems]="bulkMenuActions"
                    (menuItemClicked)="handleBulkAction($event)">
                    <span class="flex items-center">
                      <span class="font-bold">•••</span>
                      <span class="ml-1 text-gray-500">({{ selectedUsers.length }})</span>
                    </span>
                  </app-admin-menu>
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500  tracking-wider">
                  User
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500  tracking-wider">
                  Email
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500  tracking-wider">
                  Balance
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500  tracking-wider">
                  Total Spend
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500  tracking-wider">
                  <span class="hidden sm:inline">Discount & Prices</span>
                  <span class="sm:hidden">D&P</span>
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500  tracking-wider">
                  <span class="hidden sm:inline">Referrals</span>
                  <span class="sm:hidden">Ref</span>
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500  tracking-wider">
                  Status
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500  tracking-wider">
                  Last Login
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500  tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr *ngFor="let user of users" class="hover:bg-[#f0f7ff] transition-colors duration-150">
                <td class="p-4 w-4">
                  <div class="flex items-center">
                    <input type="checkbox" [checked]="isUserSelected(user.id)" (change)="toggleUserSelection(user.id)"
                      class="w-4 h-4 text-[#3b82f6] bg-white border-gray-300 rounded focus:ring-[#3b82f6]">
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  {{ user.id }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
                  {{ user.user_name }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
                  {{ user.email }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700" [ngClass]="{'text-green-600': user.balance > 0}">
                  {{ formatBalance(user.balance ) }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700" [ngClass]="{'text-red-600': user.total_spend > 0}">
                  {{ formatBalance(user.total_spend) }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex flex-col gap-1">
                    <div class="flex items-center" *ngIf="user.custom_discount > 0">
                      <span class="inline-flex items-center justify-center w-5 h-5 mr-2 bg-[#3b82f6] text-white rounded-full">
                        <fa-icon [icon]="['fas', 'percent']" [size]="'xs'"></fa-icon>
                      </span>
                      <span class="text-sm text-gray-900 font-medium hidden sm:inline">{{ user.custom_discount }}% custom discount</span>
                      <span class="text-sm text-gray-900 font-medium sm:hidden">{{ user.custom_discount }}%</span>
                    </div>
                    <div class="flex items-center" *ngIf="user.special_price_count > 0">
                      <span class="inline-flex items-center justify-center w-5 h-5 mr-2 bg-[#f97316] text-white rounded-full">
                        <fa-icon [icon]="['fas', 'tag']" [size]="'xs'"></fa-icon>
                      </span>
                      <span class="text-sm text-gray-900 font-medium hidden sm:inline">{{ user.special_price_count }} special prices</span>
                      <span class="text-sm text-gray-900 font-medium sm:hidden">{{ user.special_price_count }}</span>
                    </div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex items-center" *ngIf="user.custom_referral_rate > 0">
                    <span class="inline-flex items-center justify-center w-5 h-5 mr-2 bg-[#10b981] text-white rounded-full">
                      <fa-icon [icon]="['fas', 'users']" [size]="'xs'"></fa-icon>
                    </span>
                    <span class="text-sm text-gray-900 font-medium hidden sm:inline">{{ user.custom_referral_rate }}% referral rate</span>
                    <span class="text-sm text-gray-900 font-medium sm:hidden">{{ user.custom_referral_rate }}%</span>
                  </div>
                  <div class="flex items-center" *ngIf="!user.custom_referral_rate || user.custom_referral_rate === 0">
                    <span class="text-sm text-gray-500">Default rate</span>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span
                    [ngClass]="{
                      'bg-green-100 text-green-800': user.status === 'ACTIVATED',
                      'bg-red-100 text-red-800': user.status === 'DEACTIVATED'
                    }"
                    class="px-2 py-1 text-xs font-medium rounded-full">
                    {{ user.status }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
                  {{ user.last_login_at | date:'short' }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-gray-700 text-sm font-medium">
                  <div>
                    <app-admin-menu
                      [menuItems]="getMenuActionsForUser(user)"
                      (menuItemClicked)="handleMenuAction($event, user.id)">
                      <fa-icon [icon]="['fas', 'ellipsis']"></fa-icon>
                    </app-admin-menu>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- Mobile Card View (shown only on mobile) -->
    <div class="md:hidden">
      <!-- Select All and Bulk Actions for Mobile -->
      <div class="flex justify-between items-center mb-4 px-2">
        <div class="flex items-center">
          <input type="checkbox" [(ngModel)]="selectAll" (change)="toggleAllUsers()"
            class="w-4 h-4 text-[#3b82f6] bg-white border-gray-300 rounded focus:ring-[#3b82f6]">
          <span class="ml-2 text-sm text-gray-700">Select All</span>
        </div>
        <app-admin-menu *ngIf="selectedUsers.length > 0"
          [menuItems]="bulkMenuActions"
          (menuItemClicked)="handleBulkAction($event)">
          <span class="flex items-center">
            <fa-icon [icon]="['fas', 'ellipsis']"></fa-icon>
            <span class="ml-1 text-gray-500">({{ selectedUsers.length }})</span>
          </span>
        </app-admin-menu>
      </div>

      <!-- User Cards -->
      <div class="space-y-4">
        <div *ngFor="let user of users"
             class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:border-[#3b82f6] transition-colors duration-150">
          <!-- Card Header with User Info and Checkbox -->
          <div class="flex justify-between items-center p-4 border-b border-gray-100">
            <div class="flex items-center">
              <input type="checkbox" [checked]="isUserSelected(user.id)" (change)="toggleUserSelection(user.id)"
                class="w-4 h-4 text-[#3b82f6] bg-white border-gray-300 rounded focus:ring-[#3b82f6]">
              <div class="ml-3">
                <div class="font-medium text-gray-900">{{ user.user_name }}</div>
                <div class="text-sm text-gray-500">ID: {{ user.id }}</div>
              </div>
            </div>
            <span
              [ngClass]="{
                'bg-green-100 text-green-800': user.status === 'ACTIVATED',
                'bg-red-100 text-red-800': user.status === 'DEACTIVATED'
              }"
              class="px-2 py-1 text-xs font-medium rounded-full">
              {{ user.status }}
            </span>
          </div>

          <!-- Card Body with User Details -->
          <div class="p-4 space-y-3">
            <!-- Email -->
            <div class="flex items-center">
              <fa-icon [icon]="['fas', 'envelope']" class="text-gray-400 w-5"></fa-icon>
              <span class="ml-2 text-sm text-gray-700">{{ user.email }}</span>
            </div>

            <!-- Balance -->
            <div class="flex items-center">
              <fa-icon [icon]="['fas', 'wallet']" class="text-gray-400 w-5"></fa-icon>
              <span class="ml-2 text-sm" [ngClass]="{'text-green-600': user.balance > 0, 'text-gray-700': user.balance <= 0}">
                {{ formatBalance(user.balance)  }}
              </span>
            </div>

            <!-- Total Spend -->
            <div class="flex items-center">
              <fa-icon [icon]="['fas', 'credit-card']" class="text-gray-400 w-5"></fa-icon>
              <span class="ml-2 text-sm" [ngClass]="{'text-red-600': user.total_spend > 0, 'text-gray-700': user.total_spend <= 0}">
                Total Spend: ${{ formatBalance(user.total_spend) }}
              </span>
            </div>

            <!-- Discount & Prices -->
            <div *ngIf="user.custom_discount > 0 || user.special_price_count > 0" class="flex items-start">
              <fa-icon [icon]="['fas', 'tag']" class="text-gray-400 w-5 mt-1"></fa-icon>
              <div class="ml-2 space-y-1">
                <div *ngIf="user.custom_discount > 0" class="text-sm text-gray-900">
                  <span class="inline-flex items-center justify-center w-4 h-4 mr-1 bg-[#3b82f6] text-white rounded-full">
                    <fa-icon [icon]="['fas', 'percent']" [size]="'xs'"></fa-icon>
                  </span>
                  {{ user.custom_discount }}% custom discount
                </div>
                <div *ngIf="user.special_price_count > 0" class="text-sm text-gray-900">
                  <span class="inline-flex items-center justify-center w-4 h-4 mr-1 bg-[#f97316] text-white rounded-full">
                    <fa-icon [icon]="['fas', 'tag']" [size]="'xs'"></fa-icon>
                  </span>
                  {{ user.special_price_count }} special prices
                </div>
              </div>
            </div>

            <!-- Referrals -->
            <div class="flex items-center">
              <fa-icon [icon]="['fas', 'users']" class="text-gray-400 w-5"></fa-icon>
              <div class="ml-2 text-sm">
                <span *ngIf="user.custom_referral_rate > 0" class="text-gray-900">
                  {{ user.custom_referral_rate }}% referral rate
                </span>
                <span *ngIf="!user.custom_referral_rate || user.custom_referral_rate === 0" class="text-gray-500">
                  Default rate
                </span>
              </div>
            </div>

            <!-- Last Login -->
            <div class="flex items-center">
              <fa-icon [icon]="['fas', 'clock']" class="text-gray-400 w-5"></fa-icon>
              <span class="ml-2 text-sm text-gray-700">{{ user.last_login_at | date:'medium' }}</span>
            </div>
          </div>

          <!-- Card Footer with Actions -->
          <div class="px-4 py-3 flex justify-end">
            <app-admin-menu
              [menuItems]="getMenuActionsForUser(user)"
              (menuItemClicked)="handleMenuAction($event, user.id)"
              [dropdownOptions]="{ placement: 'top-right' }">
              <fa-icon [icon]="['fas', 'ellipsis']"></fa-icon>
            </app-admin-menu>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Pagination -->
  <div *ngIf="users.length > 0" class="flex md:justify-between md:items-center mt-4 pagination-container">
    <div class="text-sm text-gray-700">
      Showing <span class="font-medium">{{ pagination.pageNumber * pagination.pageSize + 1 }}</span> to
      <span class="font-medium">{{ (pagination.pageNumber * pagination.pageSize) + users.length }}</span> of
      <span class="font-medium">{{ pagination.totalElements }}</span> results
    </div>
    <div class="flex space-x-2 md:justify-end justify-center mt-4 md:mt-0">
      <button
        [disabled]="pagination.pageNumber === 0"
        [ngClass]="{'opacity-50 cursor-not-allowed': pagination.pageNumber === 0}"
        (click)="loadUsers(pagination.pageNumber - 1)"
        class="px-3 py-1 border border-gray-300 rounded-md text-sm">
        Previous
      </button>
      <button
        [disabled]="pagination.pageNumber >= pagination.totalPages - 1"
        [ngClass]="{'opacity-50 cursor-not-allowed': pagination.pageNumber >= pagination.totalPages - 1}"
        (click)="loadUsers(pagination.pageNumber + 1)"
        class="px-3 py-1 border border-gray-300 rounded-md text-sm">
        Next
      </button>
    </div>
  </div>

  <!-- No results message -->
  <div *ngIf="users.length === 0" class="text-center py-8">
    <div class="flex flex-col items-center justify-center">
      <fa-icon [icon]="['fas', 'search']" class="text-gray-300 text-4xl mb-3"></fa-icon>
      <p class="text-gray-500">No users found matching your criteria.</p>
      <button (click)="resetSearch()" class="mt-4 px-4 py-2 bg-[#3b82f6] text-white rounded-lg hover:bg-[#2563eb] transition-colors duration-200">
        Reset Search
      </button>
    </div>
  </div>
</div>

<!-- Old dropdown menus removed and replaced with app-admin-menu components -->

<!-- Balance Management Popup -->
<app-manage-balance
  *ngIf="showBalancePopup"
  [userId]="selectedUserId"
  [userName]="selectedUserName"
  [currentBalance]="selectedUserBalance"
  (close)="closeBalancePopup()"
  (balanceUpdated)="updateUserBalance($event)">
</app-manage-balance>

<!-- Edit User Popup -->
<app-edit-user
  *ngIf="showEditUserPopup"
  [userId]="selectedUserId"
  [user]="selectedUser"
  (close)="closeEditUserPopup()"
  (userUpdated)="updateUser($event)">
</app-edit-user>

<!-- Ban Account Popup -->
<app-ban-account
  *ngIf="showBanAccountPopup"
  [userId]="selectedUserId"
  [userName]="selectedUserName"
  [userStatus]="selectedUserStatus"
  [userIds]="selectedUsers"
  [users]="users"
  (close)="closeBanAccountPopup()"
  (statusChanged)="updateUserStatus($event)">
</app-ban-account>

<!-- Reset Password Popup -->
<app-reset-password
  *ngIf="showResetPasswordPopup"
  [userId]="selectedUserId"
  [userName]="selectedUserName"
  (close)="closeResetPasswordPopup()">
</app-reset-password>

<!-- Special Prices User Popup -->
<app-special-prices-user
  *ngIf="showSpecialPricesUserPopup"
  [user]="selectedUser"
  [userIdNumber]="selectedUserId"
  (close)="closeSpecialPricesUserPopup()"
  (specialPriceAdded)="onSpecialPriceAdded($event)">
</app-special-prices-user>

<!-- User Referrals Popup -->
<app-user-referrals
  *ngIf="showUserReferralsPopup"
  [userId]="selectedUserId"
  [userName]="selectedUserName"
  [customReferralRate]="selectedUser?.custom_referral_rate || 0"
  (close)="closeUserReferralsPopup()">
</app-user-referrals>

<!-- Payment History Popup -->
<app-payment-history
  *ngIf="showPaymentHistoryPopup"
  [userId]="selectedUserId"
  [userName]="selectedUserName"
  (close)="closePaymentHistoryPopup()">
</app-payment-history>
